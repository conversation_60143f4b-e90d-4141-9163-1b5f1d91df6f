<script lang="ts">
	import type { EnhancedWbsItemTree } from '$lib/budget_utils';

	let { budgetData }: { budgetData: EnhancedWbsItemTree[] } = $props();

	// Format currency
	function formatCurrency(value: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD',
			minimumFractionDigits: 0,
			maximumFractionDigits: 0,
		}).format(value);
	}

	// Calculate total budget
	const totalBudget = $derived(() => {
		if (!budgetData || budgetData.length === 0) return 0;
		return budgetData.reduce((sum, item) => sum + item.totalCost, 0);
	});

	// Flatten the hierarchy for display
	function flattenHierarchy(
		nodes: EnhancedWbsItemTree[],
		level = 0,
	): Array<EnhancedWbsItemTree & { level: number }> {
		const result: Array<EnhancedWbsItemTree & { level: number }> = [];

		for (const node of nodes) {
			result.push({ ...node, level });
			if (node.children && node.children.length > 0) {
				result.push(...flattenHierarchy(node.children, level + 1));
			}
		}

		return result;
	}

	const flattenedData = $derived(() => {
		if (!budgetData || budgetData.length === 0) return [];
		return flattenHierarchy(budgetData);
	});
</script>

{#if budgetData && budgetData.length > 0}
	<div class="w-full">
		<!-- Budget Summary -->
		<div class="mb-6 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-6">
			<h3 class="text-xl font-semibold text-gray-900">Project Budget Overview</h3>
			<div class="mt-2 text-3xl font-bold text-blue-600">
				{formatCurrency(totalBudget())}
			</div>
			<div class="mt-1 text-sm text-gray-600">
				Total across {budgetData.length} top-level categor{budgetData.length === 1 ? 'y' : 'ies'}
			</div>
		</div>

		<!-- Hierarchical Budget Display -->
		<div class="space-y-2">
			{#each flattenedData() as item (item.nodeId)}
				<div
					class="rounded-lg border bg-white p-4 shadow-sm transition-shadow hover:shadow-md"
					style="margin-left: {item.level * 1.5}rem"
				>
					<div class="flex items-center justify-between">
						<div class="flex-1">
							<div class="flex items-center gap-2">
								<span class="font-mono text-sm text-gray-500">{item.code}</span>
								<span class="font-medium text-gray-900">{item.description}</span>
								{#if item.children.length > 0}
									<span class="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600">
										{item.children.length} sub-item{item.children.length === 1 ? '' : 's'}
									</span>
								{/if}
							</div>

							{#if item.directCost > 0 || item.childrenCost > 0}
								<div class="mt-2 flex gap-4 text-sm text-gray-600">
									{#if item.directCost > 0}
										<span>Direct: {formatCurrency(item.directCost)}</span>
									{/if}
									{#if item.childrenCost > 0}
										<span>Children: {formatCurrency(item.childrenCost)}</span>
									{/if}
								</div>
							{/if}
						</div>

						<div class="text-right">
							<div class="text-lg font-semibold text-gray-900">
								{formatCurrency(item.totalCost)}
							</div>
							{#if totalBudget() > 0}
								<div class="text-sm text-gray-500">
									{((item.totalCost / totalBudget()) * 100).toFixed(1)}%
								</div>
							{/if}
						</div>
					</div>

					<!-- Progress bar showing relative cost -->
					{#if totalBudget() > 0}
						<div class="mt-3">
							<div class="h-2 w-full rounded-full bg-gray-200">
								<div
									class="h-2 rounded-full bg-gradient-to-r from-blue-400 to-blue-600"
									style="width: {Math.min((item.totalCost / totalBudget()) * 100, 100)}%"
								></div>
							</div>
						</div>
					{/if}
				</div>
			{/each}
		</div>
	</div>
{:else}
	<div class="flex h-64 items-center justify-center text-gray-500">
		<div class="text-center">
			<div class="text-lg font-medium">No Budget Data</div>
			<div class="mt-1 text-sm">No budget items have been created for this project yet.</div>
		</div>
	</div>
{/if}
