import { requireUser, requireProject } from '$lib/server/auth';
import { buildBudgetTree } from '$lib/budget_utils';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, cookies }) => {
	await requireUser(cookies);

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject(params, cookies);

	// Fetch the project data to get project_id and wbs_library_id
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return {
			budgetData: null,
		};
	}

	// Fetch WBS items with their current budget items for the sunburst chart
	const { data: rawNodes, error: wbsError } = await supabase
		.from('wbs_library_item')
		.select(
			`*,
      budget_line_item_current(*)
    `,
		)
		.eq('wbs_library_id', projectData.wbs_library_id)
		.eq('budget_line_item_current.project_id', projectData.project_id)
		.or(
			`client_id.eq.${projectData.client.client_id},project_id.eq.${projectData.project_id},item_type.eq.Standard`,
		);

	if (wbsError) {
		console.error('Error fetching WBS items:', wbsError);
		return {
			budgetData: null,
		};
	}

	// Build the budget tree for the sunburst chart
	const budgetData = rawNodes && rawNodes.length > 0 ? buildBudgetTree(rawNodes) : null;

	return {
		budgetData,
	};
};
